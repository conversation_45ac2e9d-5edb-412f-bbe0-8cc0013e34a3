{"cells": [{"cell_type": "code", "execution_count": 38, "id": "712d75fc", "metadata": {}, "outputs": [], "source": ["# BÖLÜM 0 GEREKLİ KÜTÜPHANELER✅\n", "import pandas as pd\n", "from itertools import product\n", "import tkinter as tk\n", "from tkinter import ttk, filedialog, messagebox\n", "import tkinter.filedialog as fd\n"]}, {"cell_type": "code", "execution_count": 39, "id": "ebf46648", "metadata": {}, "outputs": [], "source": ["# BÖLÜM 0.1 GUİ PENCERESİ OLUŞTURMA VE VERİLERİ TOPLAMA\n", "\n", "il_aktarma_dict = {\n", "    \"ADANA\": [\"ADANA\"],\n", "    \"ADIYAMAN\": [\"GAZİANTEP\"],\n", "    \"AFYONKARAHİSAR\": [\"AFYON\"],\n", "    \"AĞRI\": [\"ERZURUM\", \"VAN\"],\n", "    \"AKSARAY\": [\"AKSARAY\"],\n", "    \"AMASYA\": [\"MERZİFON\"],\n", "    \"ANKARA\": [\"AKSARAY\", \"ANKARA\"],\n", "    \"ANTALYA\": [\"ANTALYA\", \"FETHİYE\"],\n", "    \"ARDAHAN\": [\"ERZURUM\"],\n", "    \"ARTVİN\": [\"TRABZON\"],\n", "    \"AYDIN\": [\"DENİZLİ\", \"EGE\", \"MUĞLA\"],\n", "    \"BALIKESİR\": [\"BALIKESİR\"],\n", "    \"BARTIN\": [\"DÜZCE\"],\n", "    \"BATMAN\": [\"DİYARBAKIR\"],\n", "    \"BAYBURT\": [\"ERZURUM\"],\n", "    \"BİLECİK\": [\"BURSA\"],\n", "    \"BİNGÖL\": [\"ELAZIĞ\"],\n", "    \"BİTLİS\": [\"ELAZIĞ\"],\n", "    \"BOLU\": [\"DÜZCE\"],\n", "    \"BURDUR\": [\"ANTALYA\"],\n", "    \"BURSA\": [\"BURSA\"],\n", "    \"ÇANAKKALE\": [\"ÇANAKKALE\"],\n", "    \"ÇANKIRI\": [\"ANKARA\"],\n", "    \"ÇORUM\": [\"MERZİFON\"],\n", "    \"DENİZLİ\": [\"DENİZLİ\"],\n", "    \"DİYARBAKIR\": [\"DİYARBAKIR\"],\n", "    \"DÜZCE\": [\"DÜZCE\"],\n", "    \"EDİRNE\": [\"BABAESKİ\"],\n", "    \"ELAZIĞ\": [\"ELAZIĞ\"],\n", "    \"ERZİNCAN\": [\"SİVAS\"],\n", "    \"ERZURUM\": [\"ERZURUM\"],\n", "    \"ESKİŞEHİR\": [\"ESKİŞEHİR\"],\n", "    \"GAZİANTEP\": [\"GAZİANTEP\"],\n", "    \"GİRESUN\": [\"SAMSUN\"],\n", "    \"GÜMÜŞHANE\": [\"ERZURUM\", \"TRABZON\"],\n", "    \"HAKKARİ\": [\"VAN\"],\n", "    \"HATAY\": [\"HATAY\"],\n", "    \"IĞDIR\": [\"VAN\"],\n", "    \"ISPARTA\": [\"AFYON\"],\n", "    \"İSTANBUL\": [\"ANADOLU\", \"AVRUPA\"],\n", "    \"İZMİR\": [\"EGE\", \"İZMİR\", \"MANİSA\"],\n", "    \"KAHRAMANMARAŞ\": [\"GAZİANTEP\"],\n", "    \"KARABÜK\": [\"DÜZCE\"],\n", "    \"KARAMAN\": [\"KONYA\"],\n", "    \"KARS\": [\"ERZURUM\"],\n", "    \"KASTAMONU\": [\"ANKARA\"],\n", "    \"KAYSERİ\": [\"KAYSERİ\"],\n", "    \"KIRIKKALE\": [\"ANKARA\"],\n", "    \"KIRKLARELİ\": [\"BABAESKİ\", \"TRAKYA\"],\n", "    \"KIRŞEHİR\": [\"AKSARAY\", \"KAYSERİ\"],\n", "    \"KİLİS\": [\"GAZİANTEP\"],\n", "    \"KOCAELİ\": [\"ANADOLU\", \"KOCAELİ\"],\n", "    \"KONYA\": [\"AKSARAY\", \"KONYA\"],\n", "    \"KÜTAHYA\": [\"AFYON\"],\n", "    \"MALATYA\": [\"MALATYA\"],\n", "    \"MANİSA\": [\"MANİSA\"],\n", "    \"MARDİN\": [\"MARDİN\"],\n", "    \"MERSİN\": [\"MERSİN\"],\n", "    \"MUĞLA\": [\"BODRUM\", \"FETHİYE\", \"MUĞLA\"],\n", "    \"MUŞ\": [\"ELAZIĞ\"],\n", "    \"NEVŞEHİR\": [\"AKSARAY\"],\n", "    \"NİĞDE\": [\"AKSARAY\"],\n", "    \"ORDU\": [\"SAMSUN\"],\n", "    \"OSMANİYE\": [\"ADANA\"],\n", "    \"RİZE\": [\"TRABZON\"],\n", "    \"SAKARYA\": [\"KOCAELİ\"],\n", "    \"SAMSUN\": [\"MER<PERSON><PERSON><PERSON><PERSON>\", \"SAMSUN\"],\n", "    \"SİİRT\": [\"DİYARBAKIR\"],\n", "    \"SİNOP\": [\"MER<PERSON><PERSON><PERSON><PERSON>\", \"SAMSUN\"],\n", "    \"SİVAS\": [\"SİVAS\"],\n", "    \"ŞANLIURFA\": [\"GAZİANTEP\", \"ŞANLIURFA\"],\n", "    \"ŞIRNAK\": [\"MARDİN\"],\n", "    \"TEKİRDAĞ\": [\"BABAESKİ\", \"TRAKYA\"],\n", "    \"TOKAT\": [\"MERZİFON\"],\n", "    \"TRABZON\": [\"TRABZON\"],\n", "    \"TUNCELİ\": [\"ELAZIĞ\"],\n", "    \"UŞAK\": [\"AFYON\"],\n", "    \"VAN\": [\"VAN\"],\n", "    \"YALOVA\": [\"BURSA\"],\n", "    \"YOZGAT\": [\"KAYSERİ\"],\n", "    \"ZONGULDAK\": [\"DÜZCE\"]\n", "}\n", "\n", "def aktarma_guncelle(event):\n", "    secilen_il = secim_cıkıs_il_var.get()\n", "    aktarmalar = il_aktarma_dict.get(secilen_il, [])\n", "    secim_cıkıs_aktarma_var.set('')\n", "    aktarma_combo['values'] = aktarmalar\n", "    if akt<PERSON>lar:\n", "        aktarma_combo.current(0)\n", "    else:\n", "        aktarma_combo.set('')\n", "\n", "def kaydet_ve_kapat():\n", "    global secim, secim_ay, secim_yıl, secim_maliyet, secim_sube_maliyet, secim_pickup_maliyet, secim_cıkıs_aktarma, secim_cıkıs_il\n", "    secim = secim_var.get()\n", "    secim_ay = secim_ay_var.get()\n", "    secim_yıl = secim_yıl_var.get()\n", "    secim_maliyet = secim_maliyet_var.get()\n", "    try:\n", "        secim_sube_maliyet = float(secim_sube_maliyet_var.get())\n", "    except ValueError:\n", "        secim_sube_maliyet = 1.0\n", "    try:\n", "        secim_pickup_maliyet = float(secim_pickup_maliyet_var.get())\n", "    except ValueError:\n", "        secim_pickup_maliyet = 1.0\n", "    secim_cıkıs_il = secim_cıkıs_il_var.get()\n", "    secim_cıkıs_aktarma = secim_cıkıs_aktarma_var.get()\n", "    root.destroy()\n", "\n", "# GUI başlat\n", "root = tk.Tk()\n", "root.title(\"<PERSON><PERSON><PERSON>\")\n", "\n", "# Tk değişkenleri\n", "secim_var = tk.StringVar(value=\"İL\")\n", "secim_ay_var = tk.StringVar(value=\"OCAK\")\n", "secim_yıl_var = tk.StringVar(value=\"2025\")\n", "secim_maliyet_var = tk.StringVar(value=\"DAĞITIM\")\n", "secim_sube_maliyet_var = tk.StringVar(value=\"0.0\")\n", "secim_pickup_maliyet_var = tk.StringVar(value=\"0.0\")\n", "secim_cıkıs_aktarma_var = tk.StringVar()\n", "secim_cıkıs_il_var = tk.StringVar()\n", "\n", "# Etiket ve bileşenler\n", "ttk.Label(root, text=\"<PERSON><PERSON><PERSON> (İL/İLÇE):\").grid(row=0, column=0, sticky=\"w\", padx=5, pady=5)\n", "ttk.Combobox(root, textvariable=secim_var, values=[\"İL\", \"İLÇE\"]).grid(row=0, column=1, padx=5, pady=5)\n", "\n", "ttk.Label(root, text=\"Seçim Ay:\").grid(row=1, column=0, sticky=\"w\", padx=5, pady=5)\n", "ttk.Combobox(root, textvariable=secim_ay_var, values=[\"OCAK\", \"ŞUBAT\", \"MART\", \"NİSAN\", \"MAYIS\", \"HAZİRAN\", \"TEMMUZ\", \"AĞUSTOS\", \"EYLÜL\", \"EKİM\", \"KASIM\", \"ARALIK\"]).grid(row=1, column=1, padx=5, pady=5)\n", "\n", "ttk.Label(root, text=\"<PERSON><PERSON>im <PERSON>\").grid(row=2, column=0, sticky=\"w\", padx=5, pady=5)\n", "ttk.Combobox(root, textvariable=secim_yıl_var, values=[\"2025\", \"2026\", \"2027\"]).grid(row=2, column=1, padx=5, pady=5)\n", "\n", "ttk.Label(root, text=\"Seçim <PERSON>t:\").grid(row=3, column=0, sticky=\"w\", padx=5, pady=5)\n", "ttk.Combobox(root, textvariable=secim_maliyet_var, values=[\"DAĞITIM\", \"PALET TAŞIMA\", \"ZİNCİR MAĞAZA\"]).grid(row=3, column=1, padx=5, pady=5)\n", "\n", "ttk.Label(root, text=\"Seçim <PERSON>ube Maliyet:\").grid(row=4, column=0, sticky=\"w\", padx=5, pady=5)\n", "ttk.Entry(root, textvariable=secim_sube_maliyet_var).grid(row=4, column=1, padx=5, pady=5)\n", "\n", "ttk.Label(root, text=\"Seçim Pickup Maliyet:\").grid(row=5, column=0, sticky=\"w\", padx=5, pady=5)\n", "ttk.Entry(root, textvariable=secim_pickup_maliyet_var).grid(row=5, column=1, padx=5, pady=5)\n", "\n", "ttk.Label(root, text=\"<PERSON><PERSON><PERSON>ıkış İl:\").grid(row=6, column=0, sticky=\"w\", padx=5, pady=5)\n", "il_combo = ttk.Combobox(root, textvariable=secim_cıkıs_il_var, values=sorted(il_aktarma_dict.keys()))\n", "il_combo.grid(row=6, column=1, padx=5, pady=5)\n", "il_combo.bind(\"<<ComboboxSelected>>\", aktarma_guncelle)\n", "\n", "ttk.Label(root, text=\"<PERSON><PERSON>im Çıkış Aktarma:\").grid(row=7, column=0, sticky=\"w\", padx=5, pady=5)\n", "aktarma_combo = ttk.Combobox(root, textvariable=secim_cıkıs_aktarma_var)\n", "aktarma_combo.grid(row=7, column=1, padx=5, pady=5)\n", "\n", "ttk.Button(root, text=\"<PERSON><PERSON> ve <PERSON>\", command=kaydet_ve_kapat).grid(row=8, column=0, columnspan=2, pady=10)\n", "\n", "# GUI çalıştır\n", "root.mainloop()"]}, {"cell_type": "code", "execution_count": 40, "id": "e590a614", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py:329: UserWarning: Slicer List extension is not supported and will be removed\n", "  warn(msg)\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py:329: UserWarning: Unknown extension is not supported and will be removed\n", "  warn(msg)\n"]}], "source": ["# BÖLÜM 0.2 GUİ PENCERESİ OLUŞTURMA VE EXCEL YÜKLEME\n", "\n", "\n", "# Global değişkenler\n", "excel_yolu = \"\"\n", "excel_yolu_desi = \"\"\n", "df_maliyetler = None\n", "df_desi = None\n", "df_toplama = None\n", "df_kırılım = None\n", "df_hat = None\n", "\n", "def dosya_sec(entry_var):\n", "    yol = filedialog.askopenfilename(filetypes=[(\"Excel Dosyası\", \"*.xlsx\")])\n", "    if yol:\n", "        entry_var.set(yol)\n", "\n", "def desi_durumu_degisti():\n", "    if desi_checkbox_var.get():\n", "        desi_yolu_entry.configure(state='normal')\n", "        desi_sec_button.configure(state='normal')\n", "    else:\n", "        desi_yolu_entry.configure(state='disabled')\n", "        desi_sec_button.configure(state='disabled')\n", "\n", "def verileri_yukle_ve_devam():\n", "    global excel_yolu, excel_yolu_desi, df_ma<PERSON><PERSON><PERSON>, df_desi, df_<PERSON><PERSON><PERSON>, df_k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, df_hat\n", "\n", "    yol_maliyet = maliyet_yolu_var.get()\n", "    if not yol_maliyet:\n", "        messagebox.showerror(\"<PERSON><PERSON>\", \"Lütfen maliyet dosyasını seçin.\")\n", "        return\n", "\n", "    try:\n", "        df = pd.read_excel(yol_maliyet, sheet_name=None)\n", "        df_maliyetler = df[\"TÜMÜ\"]\n", "        df_toplama = df[\"toplama_elleçleme\"]\n", "        df_kırılım = df[\"kırılım\"]\n", "        df_hat = df[\"kırılım_hat\"]\n", "        excel_yolu = yol_maliyet\n", "\n", "        if desi_checkbox_var.get():\n", "            yol_desi = desi_yolu_var.get()\n", "            if not yol_desi:\n", "                messagebox.showerror(\"<PERSON><PERSON>\", \"Lütfen desi dosyasını seçin.\")\n", "                return\n", "            df_desi = pd.read_excel(yol_desi)\n", "            excel_yolu_desi = yol_desi\n", "        else:\n", "            df_desi = df[\"desi\"]\n", "\n", "    except Exception as e:\n", "        messagebox.showerror(\"Dosya Hatası\", f\"Excel dosyası okunamadı:\\n{e}\")\n", "        return\n", "\n", "    root2.destroy()\n", "    #print(\"📁 Veriler başarıyla yüklendi.\")\n", "    #print(f\"Maliyet Dosyası: {excel_yolu}\")\n", "    #print(f\"<PERSON><PERSON>: {excel_yolu_desi if desi_checkbox_var.get() else '(ilk excelden geldi)'}\")\n", "\n", "# Yeni pencere\n", "root2 = tk.Tk()\n", "root2.title(\"B<PERSON>lüm 2 - <PERSON><PERSON> Yükleme\")\n", "\n", "# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "maliyet_yolu_var = tk.StringVar()\n", "desi_yolu_var = tk.StringVar()\n", "desi_checkbox_var = tk.BooleanVar()\n", "\n", "# Maliyet Dosyası\n", "ttk.Label(root2, text=\"Maliyet Excel Dosyası:\\n Birim Maliyetleri 2025 Liste.xlsx dosyasına göre hazırlanmıştır.\").grid(row=0, column=0, sticky=\"w\", padx=5, pady=5)\n", "ttk.Entry(root2, textvariable=maliyet_yolu_var, width=50).grid(row=0, column=1, padx=5, pady=5)\n", "ttk.Button(root2, text=\"Gözat\", command=lambda: dosya_sec(maliyet_yolu_var)).grid(row=0, column=2, padx=5, pady=5)\n", "\n", "# Desi Ayrı Dosya Seçimi\n", "ttk.Checkbutton(root2, text=\"Desi ayrı dosyada\\n Eğer ayrıca desi girmeyecekseni burayı boş bırakın.\\n Sekme Adı 'desi' olmalı ve sütunlar sırasıyla 'Dönem', '<PERSON><PERSON><PERSON><PERSON><PERSON> İl', 'Çıkış Aktarma', 'Varış İl', 'Varış Aktarma', 'Toplam Desi' olmalı.\", variable=desi_checkbox_var, command=desi_durumu_degisti).grid(row=1, column=0, columnspan=2, sticky=\"w\", padx=5, pady=5)\n", "\n", "# <PERSON><PERSON>\n", "ttk.Label(root2, text=\"Desi Excel Dosyası:\").grid(row=2, column=0, sticky=\"w\", padx=5, pady=5)\n", "desi_yolu_entry = ttk.Entry(root2, textvariable=desi_yolu_var, width=50, state='disabled')\n", "desi_yolu_entry.grid(row=2, column=1, padx=5, pady=5)\n", "\n", "desi_sec_button = ttk.But<PERSON>(root2, text=\"Gözat\", command=lambda: dosya_sec(desi_yolu_var), state='disabled')\n", "desi_sec_button.grid(row=2, column=2, padx=5, pady=5)\n", "\n", "# <PERSON><PERSON>\n", "ttk.Button(root2, text=\"<PERSON><PERSON><PERSON> ve <PERSON>\", command=verileri_yukle_ve_devam).grid(row=3, column=0, columnspan=3, pady=15)\n", "\n", "root2.mainloop()"]}, {"cell_type": "code", "execution_count": 41, "id": "c3f183fc", "metadata": {}, "outputs": [], "source": ["# BÖLÜM 1 VE 2 KALDIRILDI GUİ PENCERESİ OLUŞTURULDU!!!\n", "\n", "\n", "#_________________________________________________________________________________\n", "# BÖLÜM 1 GEREKLİ BİLGİLER✅\n", "# secim = \"İL\"    # \"İl<PERSON><PERSON>\", \"İl\"  ?aktarma kullanmak mantıklı değil\n", "# secim_ay = \"OCAK\"   # \"Ocak\", \"Şubat\", \"Mart\"\n", "# secim_yıl = \"2025\"  # \"2025\", \"2026\", \"2027\"\n", "# secim_maliyet = \"DAĞITIM\"    # \"Dağıtım\", \"PALET TAŞIMA\", \"ZİNCİR MAĞAZA\"\n", "# secim_sube_maliyet = 1.2    # hesapladığı maliyete buraya eklenir\n", "# secim_cıkıs_aktarma = \"ELAZIĞ\"\n", "# secim_cıkıs_il = \"ELAZIĞ\"\n", "\n", "#_________________________________________________________________________________\n", "# BÖLÜM 2 VERİ OKUMA✅\n", "# excel_yolu = r\"C:\\Users\\<USER>\\OneDrive - Horoz Lojistik\\YID Satış\\Birim Fiyat Çalışmaları\\2025 Yılı Birim Fiyatlar\\Birim Maliyetleri 2025 Liste.xlsx\"\n", "\n", "# Tüm sayfaları tek seferde oku\n", "# df = pd.read_excel(excel_yolu, sheet_name=None)\n", "\n", "# Toplama/Elleçleme verileri\n", "# df_maliyetler = df[\"TÜMÜ\"]\n", "\n", "# Toplama/Elleçleme verileri\n", "# df_desi = df[\"desi\"]\n", "\n", "# Toplama/Elleçleme verileri\n", "# df_toplama = df[\"toplama_elleçleme\"]\n", "\n", "# Hat verileri\n", "# df_hat = df[\"kırılım_hat\"]\n"]}, {"cell_type": "code", "execution_count": 42, "id": "dc928cd6", "metadata": {}, "outputs": [], "source": ["#_BÖLÜM 3 DATAFRAME OLUŞTURMA✅\n", "\n", "secim_sutunlari = {\"AKTARMA\": [\"Aktarma\"],\"İL\": [\"İl\", \"Aktarma\"],\"İLÇE\": [\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON>l\", \"Aktarma\"]} # Seçime göre sütunları belirle\n", "kombinasyonlar = df_kırılım[secim_sutunlari[secim]].drop_duplicates().values.tolist() # Kombinasyonları oluştur\n", "carpaz_kombinasyonlar = list(product(kombinasyonlar, kombinasyonlar))\n", "# Kolon isimlerini <PERSON>a\n", "headers = {\"İLÇE\":['Ç<PERSON><PERSON><PERSON>ş İlçe', '<PERSON><PERSON>k<PERSON>ş İl', '<PERSON><PERSON><PERSON><PERSON>ş Aktarma','Varış İlçe', 'Varış İl', 'Varış Aktarma'],\n", "            \"İL\":['<PERSON><PERSON><PERSON><PERSON><PERSON> İl', '<PERSON><PERSON><PERSON><PERSON><PERSON> Aktarma','Var<PERSON>ş İl', 'Var<PERSON>ş Aktarma'],\n", "            \"AKTARMA\":['<PERSON><PERSON><PERSON><PERSON><PERSON> Aktarma','<PERSON>ar<PERSON>ş Aktarma']}\n", "\n", "# DataFrame'e dönüştürme ve Desi sütunu ekleme\n", "df_fiyat = pd.DataFrame(\n", "    [list(sum(pair, [])) for pair in carpaz_kombinasyonlar],\n", "    columns=headers[secim]\n", ")\n", "# print(f\"\\nFiyat Çalışması {secim} bazlı oluşturuldu. Toplam {len(df_fiyat)} satır.\")"]}, {"cell_type": "code", "execution_count": 43, "id": "f0392303", "metadata": {}, "outputs": [], "source": ["# BÖLÜM 4 İLAVE SÜTUNLARIN EKLENMESİ✅\n", "\n", "# tTOPLAMA VE ELLEÇLEME\n", "df_fiyat = pd.merge(\n", "    df_fiyat,\n", "    df_toplama[['<PERSON><PERSON><PERSON><PERSON><PERSON> İl', '<PERSON><PERSON><PERSON><PERSON><PERSON> Aktarma', '<PERSON><PERSON><PERSON><PERSON><PERSON> Toplama', '<PERSON><PERSON><PERSON><PERSON><PERSON> Elleçleme']],\n", "    on=['<PERSON><PERSON><PERSON><PERSON>ş İl', '<PERSON><PERSON><PERSON><PERSON><PERSON> Aktarma'],\n", "    how='left'\n", ")\n", "\n", "# HATLAR\n", "df_fiyat = pd.merge(\n", "    df_fiyat,\n", "    df_hat[['<PERSON><PERSON><PERSON><PERSON><PERSON> Aktarma', '<PERSON>ar<PERSON>ş Aktarma', 'Hat1', 'Hat2', 'Hat3']],\n", "    on=['<PERSON><PERSON><PERSON><PERSON>ş Aktarma', 'Varış Aktarma'],\n", "    how='left'\n", ")"]}, {"cell_type": "code", "execution_count": 44, "id": "0d3320bd", "metadata": {}, "outputs": [], "source": ["# BÖLÜM 5 MALİYET TÜRÜNE GÖRE TERİMLERİN BELİRLENMESİ\n", "\n", "# Maliyet türüne göre terim eşleştirmesi\n", "def get_maliyet_terimleri(maliyet_turu):\n", "    \"\"\"Maliyet türüne göre kullanılacak terimleri döndürür\"\"\"\n", "    terimler = {\n", "        'KİRA': 'KİRA',\n", "        'HAT': 'HAT'\n", "    }\n", "    \n", "    if maliyet_turu == \"DAĞITIM\":\n", "        terimler.update({\n", "            'DAĞITIM': 'DAĞITIM',\n", "            'TOPLAMA': 'TOPLAMA',\n", "            'ELLEÇLEME': 'ELLEÇLEME'\n", "        })\n", "    <PERSON><PERSON>_turu == \"PALET TAŞIMA\":\n", "        terimler.update({\n", "            'DAĞITIM': 'PALET TAŞIMA',\n", "            'TOPLAMA': 'PALET TAŞIMA',\n", "            'ELLEÇLEME': 'PALET ELLEÇLEME'\n", "        })\n", "    <PERSON><PERSON>_turu == \"ZİNCİR MAĞAZA\":\n", "        terimler.update({\n", "            'DAĞITIM': 'ZİNCİR MAĞAZA',\n", "            'TOPLAMA': 'ZİNCİR MAĞAZA',\n", "            'ELLEÇLEME': 'ELLEÇLEME'\n", "        })\n", "    \n", "    return terimler\n", "terimler = get_maliyet_terimleri(secim_maliyet)\n"]}, {"cell_type": "code", "execution_count": 45, "id": "c5a3426e", "metadata": {}, "outputs": [], "source": ["# BÖLÜM 5.2 DESİ VE ŞUBE MALİYETİ HESAPLANMASI\n", "\n", "# 👉DESİ👈\n", "df_desi_filtered = df_desi[df_desi['Dönem'] == secim_ay]\n", "df_fiyat = df_fiyat.merge(\n", "    df_desi_filtered[['<PERSON><PERSON><PERSON><PERSON>ş İl', '<PERSON><PERSON><PERSON><PERSON><PERSON> Aktarma', 'Varış Aktarma', 'Varış İl', 'Toplam Desi']],\n", "    on=['<PERSON><PERSON><PERSON><PERSON>ş İl', '<PERSON><PERSON><PERSON><PERSON><PERSON> Aktarma', 'Varış Aktarma', 'Varış İl'],\n", "    how='left'\n", ")\n", "\n", "df_fiyat['<PERSON><PERSON>'] = df_fiyat['Toplam Desi'].fillna(1)\n", "df_fiyat.drop(columns='Toplam Desi', inplace=True)  # ge<PERSON><PERSON> s<PERSON>tun si<PERSON>r\n", "\n", "\n", "\n", "# 👉ŞUBE MALİYETİ👈\n", "df_fiyat['<PERSON><PERSON>t_Şube'] = secim_sube_maliyet"]}, {"cell_type": "code", "execution_count": 46, "id": "7fe2fc12", "metadata": {}, "outputs": [], "source": ["# BÖLÜM 6 MALİYETLERİN İFADE EDİLMESİ\n", "\n", "# 👉ÇIKIŞ 👈\n", "df_fiyat['<PERSON><PERSON>t_Pickup_0'] = f\"{secim_yıl},{secim_ay},{terimler['TOPLAMA']},\" + df_fiyat['Çıkış Toplama'].fillna('') + \",\" + df_fiyat.apply(lambda row: row['Çıkış İl'] if pd.notna(row['Çıkış İl']) and row['Çıkış Aktarma'] == 'ELAZIĞ' else '', axis=1).fillna('')\n", "\n", "df_fiyat['<PERSON><PERSON><PERSON>_Kira_0'] = df_fiyat.apply(\n", "    lambda row: (\n", "        f\"{secim_yıl},{secim_ay},{terimler['KİRA']},{row['Çıkış Toplama'] or ''},\"\n", "        if (row['<PERSON><PERSON><PERSON><PERSON><PERSON> Toplama'] == row['<PERSON>ı<PERSON><PERSON><PERSON> Elleçleme'] or pd.isna(row['Çık<PERSON><PERSON> Elleçleme']))\n", "        else f\"{secim_yıl},{secim_ay},{terimler['KİRA']},{row['Çıkış Toplama'] or ''},+{secim_yıl},{secim_ay},{terimler['KİRA']},{row['Çıkış Elleçleme'] or ''},\"\n", "    ),\n", "    axis=1\n", ")\n", "\n", "df_fiyat['Maliyet_Elleçleme_0'] = df_fiyat['Çıkış Elleçleme'].apply(\n", "    lambda val: f\"{secim_yıl},{secim_ay},{terimler['ELLEÇLEME']},{val},\" if pd.notna(val) else ''\n", ")\n", "\n", "df_fiyat['Maliyet_Hat_1'] = df_fiyat.apply(\n", "    lambda row: (\n", "        f\"{secim_yıl},{secim_ay},{terimler['HAT']},\" +\n", "        (\n", "            row['<PERSON><PERSON><PERSON><PERSON><PERSON> Elleçleme'] if pd.notna(row['<PERSON>ı<PERSON><PERSON><PERSON> Elleçleme']) and row['<PERSON>ık<PERSON>ş Elleçleme'] != ''\n", "            else row['<PERSON><PERSON><PERSON><PERSON>ş Aktarma'] or ''\n", "        ) +\n", "        f\",{row['Hat1']}\"\n", "        if pd.notna(row['Hat1']) and row['Hat1'] != ''\n", "        else ''\n", "    ),\n", "    axis=1\n", ")\n", "\n", "# 👉ARA 1👈\n", "df_fiyat['<PERSON><PERSON><PERSON>_Kira_1'] = df_fiyat.apply(\n", "    lambda row: (\n", "        f\"{secim_yıl},{secim_ay},{terimler['KİRA']},{row['Hat1']},\"\n", "        if pd.notna(row['Hat2']) and row['Hat2'] != ''\n", "        else ''\n", "    ),\n", "    axis=1\n", ")\n", "\n", "df_fiyat['Maliyet_Elleçleme_1'] = df_fiyat.apply(\n", "    lambda row: (\n", "        f\"{secim_yıl},{secim_ay},{terimler['ELLEÇLEME']},{row['Hat1']},\"\n", "        if pd.notna(row['Hat2']) and row['Hat2'] != ''\n", "        else ''\n", "    ),\n", "    axis=1\n", ")\n", "\n", "df_fiyat['<PERSON>yet_Hat_2'] = df_fiyat.apply(\n", "    lambda row: (\n", "        f\"{secim_yıl},{secim_ay},{terimler['HAT']},{row['Hat1'] or ''},{row['Hat2']}\"\n", "        if pd.notna(row['Hat2']) and row['Hat2'] != ''\n", "        else ''\n", "    ),\n", "    axis=1\n", ")\n", "\n", "# 👉ARA 2👈\n", "df_fiyat['<PERSON><PERSON><PERSON>_Kira_2'] = df_fiyat.apply(\n", "    lambda row: (\n", "        f\"{secim_yıl},{secim_ay},{terimler['KİRA']},{row['Hat2']},\"\n", "        if pd.notna(row['Hat3']) and row['Hat3'] != ''\n", "        else ''\n", "    ),\n", "    axis=1\n", ")\n", "\n", "df_fiyat['Maliyet_Elleçleme_2'] = df_fiyat.apply(\n", "    lambda row: (\n", "        f\"{secim_yıl},{secim_ay},{terimler['ELLEÇLEME']},{row['Hat2']},\"\n", "        if pd.notna(row['Hat3']) and row['Hat3'] != ''\n", "        else ''\n", "    ),\n", "    axis=1\n", ")\n", "\n", "df_fiyat['<PERSON>yet_Hat_3'] = df_fiyat.apply(\n", "    lambda row: (\n", "        f\"{secim_yıl},{secim_ay},{terimler['HAT']},{row['Hat2'] or ''},{row['Hat3']}\"\n", "        if pd.notna(row['Hat3']) and row['Hat3'] != ''\n", "        else ''\n", "    ),\n", "    axis=1\n", ")\n", "\n", "# 👉VARIŞ👈\n", "df_fiyat['<PERSON><PERSON><PERSON>_Kira_3'] = df_fiyat.apply(\n", "    lambda row: (\n", "        f\"{secim_yıl},{secim_ay},{te<PERSON><PERSON>['KİRA']},{row['Varış Aktarma']},\" if pd.notna(row['Hat1']) and row['Hat1'] != '' and pd.notna(row['Varış Aktarma']) else ''),\n", "    axis=1\n", ")\n", "\n", "df_fiyat['Maliyet_Elleçleme_3'] = df_fiyat.apply(\n", "    lambda row: (\n", "        f\"{secim_yıl},{secim_ay},{terim<PERSON>['ELLEÇLEME']},{row['Varış Aktarma'] or ''},{row['Varış İl'] if row['Varış Aktarma'] == 'ELAZIĞ' else ''}\" if pd.notna(row['Hat1']) and row['Hat1'] != '' and pd.notna(row['Varış Aktarma']) else ''),\n", "    axis=1\n", ")\n", "\n", "df_fiyat['<PERSON><PERSON><PERSON>_Dağı<PERSON>ım'] = df_fiyat.apply(\n", "    lambda row: f\"{secim_yıl},{secim_ay},{terimler['DAĞITIM']},{row['Varış Aktarma'] or ''},{row['Varış İl'] if row['Varış Aktarma'] == 'ELAZIĞ' else ''}\",\n", "    axis=1\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "id": "9c5a9baf", "metadata": {}, "outputs": [], "source": ["# BÖLÜM 7 OLUŞTURULAN DATAFRAME FİLTRELENMESİ VE ELLEÇLEMENİN TEMİZLENMESİ\n", "\n", "\n", "# <PERSON><PERSON><PERSON><PERSON> s<PERSON>tunlarını tanımla\n", "ellecleme_sutunlari = ['Maliyet_Elleçleme_0', 'Maliyet_Elleçleme_1', 'Maliyet_Elleçleme_2', 'Maliyet_Elleçleme_3']\n", "\n", "# <PERSON><PERSON><PERSON> edilecek anahtar kelimeler\n", "anahtar_kelimeler = ['ANADOLU', 'AVRUPA', 'DİAMOND','HADIMKÖY YID']\n", "\n", "# Her bir Elleç<PERSON>e s<PERSON>u için işlem yap\n", "for sutun in ellecleme_sutunlari:\n", "    if sutun in df_fiyat.columns:\n", "        df_fiyat[sutun] = df_fiyat.apply(\n", "            lambda row: row[sutun] if (isinstance(row[sutun], str) and \n", "                                     any(kelime in row[sutun] for kelime in anahtar_kelimeler) and\n", "                                     not ('DİAMOND' in row[sutun] and row['<PERSON><PERSON><PERSON><PERSON><PERSON>'] == row['Varış']))\n", "                          else '',\n", "            axis=1\n", "        )\n", "    else:\n", "        print(f\"Uyarı: {sutun} sütunu DataFrame'de bulunamadı\")\n", "\n", "# CSV olarak dışla aktar türkçe karakterli, sü<PERSON><PERSON> arasında \"$\" koy   ✅seçilen çıkışa göre maliyetler\n", "df_fiyat.to_csv(f\"fiyat_calisma_A.csv\", index=False, encoding='utf-8-sig', sep='$')\n", "#print(\"CSV dosyası oluşturuldu.\")\n", "\n", "\n", "# Filtreleme işlemi\n", "df_fiyat = df_fiyat[\n", "    (df_fiyat['<PERSON>ık<PERSON>ş İl'] == secim_cıkıs_il) & \n", "    (df_fiyat['<PERSON><PERSON>k<PERSON>ş Aktarma'] == secim_cıkıs_aktarma)\n", "].copy()\n", "# Filtreleme sonucu kontrol\n", "if df_fiyat.empty:\n", "    raise ValueError(\"Filtreleme sonucu hiç kayıt bulunamadı! Girilen İl ve Aktarma değerini kontrol edin!\")\n", "\n", "\n", "# <PERSON><PERSON><PERSON> kontrol\n", "# print(\"Elleçleme sütunları temizlendi. İlk 5 satır:\")\n", "# display(df_fiyat.head())"]}, {"cell_type": "code", "execution_count": 48, "id": "f187c052", "metadata": {}, "outputs": [], "source": ["#BÖLÜM 8 MALİYETLERİ TL'YE ÇEVİRME\n", "\n", "# Maliyet sütunlarının listesi (orijinal sütun isimleriyle)\n", "maliyet_sutunlari = [\n", "    'Maliyet_Pickup_0',\n", "    'Maliyet_Kira_0',\n", "    'Maliyet_Elleçleme_0',\n", "    'Maliyet_Hat_1',\n", "    'Maliyet_Kira_1',\n", "    'Maliyet_Elleçleme_1',\n", "    'Maliyet_Hat_2',\n", "    '<PERSON><PERSON>t_Kira_2',\n", "    'Maliyet_Elleçleme_2',\n", "    'Maliyet_Hat_3',\n", "    'Maliyet_Kira_3',\n", "    'Maliyet_Elleçleme_3',\n", "    'Maliyet_Dağıtım'\n", "]\n", "\n", "# df_maliyetler'de arama yapmak için dictionary oluştur (daha hızlı eri<PERSON><PERSON> için)\n", "maliyet_sozlugu = df_maliyetler.set_index('Uniq')['TL/DESİ'].to_dict()\n", "\n", "def maliyeti_guncelle(deger):\n", "    \"\"\"Maliyet değerini TL karşılığı ile değiştirir (tekli veya ikili giriş olabilir)\"\"\"\n", "    if pd.isna(deger) or not str(deger).strip():\n", "        return None  # <PERSON><PERSON> veya NaN\n", "    \n", "    toplam_tl = 0.0\n", "    parcalar = str(deger).split('+')  # '+' varsa i<PERSON> b<PERSON>, yoksa tek elemanlı olur\n", "    \n", "    for parca in parcalar:\n", "        parca = parca.strip()  # Baş/son boşluk ve sondaki virgülü temizle  .rstrip(',')\n", "        if not parca:\n", "            continue\n", "        tl_karsiligi = maliyet_sozlugu.get(parca)\n", "        if tl_ka<PERSON><PERSON><PERSON> is not None:\n", "            toplam_tl += tl_karsiligi\n", "        else:\n", "            print(f\"Uyarı: Eşleşme bulunamadı - {parca}\")\n", "    \n", "    return toplam_tl if toplam_tl > 0 else None\n", "\n", "# Her maliyet sütununu güncelle (yeni sütun oluşturmadan doğrudan güncelle)\n", "for sutun in maliyet_sutunlari:\n", "    if sutun in df_fiyat.columns:\n", "        df_fiyat[sutun] = df_fiyat[sutun].apply(maliye<PERSON>_guncelle)\n", "    else:\n", "        print(f\"Uyarı: {sutun} sütunu DataFrame'de bulunamadı\")\n", "\n", "df_fiyat['<PERSON><PERSON>t_Pickup_0']\n", "\n", "\n", "# pickup_maliyet girildi ise ve 0'dan b<PERSON>y<PERSON>k ise bu değer yeni pickup değeridir. aksi halde excelden hesaplanan getirilir.\n", "try:\n", "    pickup_maliyet = float(secim_pickup_maliyet)\n", "except (ValueErro<PERSON>, TypeError):\n", "    pickup_maliyet = 0.0\n", "\n", "if pickup_maliyet > 0:\n", "    df_fiyat['Maliyet_Pickup_0'] = pickup_maliyet\n", "\n", "\n", "# Sonuçları kontrol et\n", "# print(\"Maliyet dönüşümü tamamlandı. İlk 5 satır:\")\n", "# display(df_fiyat.head())"]}, {"cell_type": "code", "execution_count": 49, "id": "2c94fcfc", "metadata": {}, "outputs": [], "source": ["# CSV olarak dışla aktar türkçe karakterli, sü<PERSON><PERSON> arasında \"$\" koy   ✅seçilen çıkışa göre maliyetler\n", "df_fiyat.to_csv(f\"fiyat_calisma_B.csv\", index=False, encoding='utf-8-sig', sep='$')\n", "#print(\"CSV dosyası oluşturuldu.\")"]}, {"cell_type": "code", "execution_count": 50, "id": "09f1a999", "metadata": {}, "outputs": [], "source": ["# ÖZET EKRANI OLUŞTURULMASI VE ORANLARIN GİRİLMESİ\n", "\n", "# Varsayılan oranlar\n", "default_gyg = 3.58\n", "default_finans = 4.0\n", "default_hasar = 1.0\n", "default_kar = 10.0\n", "\n", "gyg_orani = default_gyg\n", "finans_orani = default_finans\n", "hasar_orani = default_hasar\n", "kar_orani = default_kar\n", "\n", "def oranlari_kaydet_ve_kapat():\n", "    global gyg_orani, finans_orani, hasar_orani, kar_orani\n", "    try:\n", "        gyg_orani = float(gyg_var.get())\n", "        finans_orani = float(finans_var.get())\n", "        hasar_orani = float(hasar_var.get())\n", "        kar_orani = float(kar_var.get())\n", "    except ValueError:\n", "        gyg_orani = default_gyg\n", "        finans_orani = default_finans\n", "        hasar_orani = default_hasar\n", "        kar_orani = default_kar\n", "    root3.destroy()\n", "\n", "def excel_kaydet_ve_cikis():\n", "    # <PERSON><PERSON><PERSON> kaydetme penceresi aç\n", "    file_path = fd.asksaveasfilename(\n", "        defaultextension=\".xlsx\",\n", "        filetypes=[(\"Excel files\", \"*.xlsx\"), (\"All files\", \"*.*\")],\n", "        title=\"Özet Tablosunu Excel Olarak Kaydet\"\n", "    )\n", "    if not file_path:\n", "        return  # kullanı<PERSON><PERSON> iptal ettiyse işlem yok\n", "\n", "    # <PERSON><PERSON><PERSON><PERSON> al (<PERSON><PERSON><PERSON><PERSON>)\n", "    try:\n", "        gyg = float(gyg_var.get())\n", "        finans = float(finans_var.get())\n", "        hasar = float(hasar_var.get())\n", "        kar = float(kar_var.get())\n", "    except ValueError:\n", "        gyg = default_gyg\n", "        finans = default_finans\n", "        hasar = default_hasar\n", "        kar = default_kar\n", "\n", "    # Detay DataFrame\n", "    detay_df = ozet_df.copy()\n", "\n", "    detay_df[\"Maliyet_TL/Desi\"] = (detay_df[\"Toplam_Maliyet_Tutar\"] / detay_df[\"Desi\"])\n", "    detay_df[\"Maliyet_TL/Desi\"] = detay_df[\"Maliyet_TL/Desi\"].round(2)\n", "\n", "    detay_df[\"Satış_TL/Desi\"] = (detay_df[\"Toplam_Maliyet_Tutar\"] / detay_df[\"Desi\"]) * (1 + (gyg + finans + hasar + kar) / 100)\n", "    detay_df[\"Satış_TL/Desi\"] = detay_df[\"Satış_TL/Desi\"].round(2)\n", "\n", "    # Genel toplam dataframe\n", "    toplam_desi = detay_df[\"Desi\"].sum()\n", "    toplam_maliyet = detay_df[\"Toplam_Maliyet_Tutar\"].sum()\n", "    toplam_gyg = toplam_maliyet * gyg / 100\n", "    toplam_finans = toplam_maliyet * finans / 100\n", "    toplam_hasar = toplam_maliyet * hasar / 100\n", "    toplam_kar = toplam_maliyet * kar / 100\n", "    toplam_satis = toplam_maliyet + toplam_gyg + toplam_finans + toplam_hasar + toplam_kar\n", "    toplam_tl_desi = round(toplam_satis / toplam_desi, 2) if toplam_desi != 0 else 0\n", "\n", "    genel_toplam_dict = {\n", "        \"DESİ TOPLAM\": [round(toplam_desi, 2)],\n", "        \"MALİYET TOPLAM\": [round(top<PERSON>_maliyet, 2)],\n", "        \"GYG\": [round(toplam_gyg, 2)],\n", "        \"FİNANS\": [round(toplam_finans, 2)],\n", "        \"HASAR\": [round(toplam_hasar, 2)],\n", "        \"KAR\": [round(top<PERSON>_kar, 2)],\n", "        \"SATIŞ\": [round(toplam_satis, 2)],\n", "        \"SATIŞ/DESİ\": [toplam_tl_desi]\n", "    }\n", "    genel_toplam_df = pd.DataFrame(genel_toplam_dict)\n", "\n", "    # Temel bilgiler dataframe\n", "    temel_bilgiler = {\n", "        #\"Parametre\": [\"secim_yıl\", \"secim_ay\", \"secim_maliyet\", \"secim_cıkıs_il\", \"secim_cıkıs_aktarma\",\"gyg_orani\", \"finans_orani\", \"hasar_orani\", \"kar_orani\"],\n", "        \"Parametre\": [\"<PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\", \"Maliyet\", \"Çıkış İl\", \"Çıkış Aktarma\",\"Gyg\", \"Finans\", \"Hasar\", \"<PERSON><PERSON>\"],\n", "        \"Değer\": [secim_yıl, secim_ay, secim_mali<PERSON>t, secim_cık<PERSON><PERSON>_il, secim_cıkıs_aktarma,gyg, finans, hasar, kar]\n", "    }\n", "    temel_bilgiler_df = pd.DataFrame(temel_bilgiler)\n", "\n", "    # Excel'e yaz\n", "    with pd.ExcelWriter(file_path) as writer:\n", "        # <PERSON><PERSON><PERSON> adlarını uygun şekilde <PERSON>\n", "        detay_df_rename = detay_df.rename(columns={\n", "            \"Varış_Birim\": \"Var<PERSON>ş\",\n", "            \"Desi\": \"DESİ\",\n", "            \"Toplam_Maliyet_Tutar\": \"Maliyet Toplam\"\n", "        })\n", "        detay_df_rename[[\"Var<PERSON>ş\", \"DESİ\", \"Maliyet_TL/Desi\", \"Satış_TL/Desi\"]].to_excel(writer, index=False, sheet_name=\"Detay\")\n", "        genel_toplam_df.to_excel(writer, index=False, sheet_name=\"Genel Toplam\")\n", "        temel_bilgiler_df.to_excel(writer, index=False, sheet_name=\"Temel Bilgiler\")\n", "\n", "    root3.destroy()\n", "\n", "\n", "\n", "root3 = tk.Tk()\n", "root3.title(\"<PERSON><PERSON>t ve Oran G<PERSON>şi\")\n", "\n", "# Başlık\n", "ozet_baslik = f\"{secim_yıl}, {secim_ay}, {secim_maliyet} Fiyat Çalışması \\nÇıkış İl {secim_cıkıs_il}, \\nÇıkış Aktarma {secim_cıkıs_aktarma}\"\n", "ttk.Label(root3, text=ozet_baslik, font=(\"Arial\", 12, \"bold\")).grid(row=0, column=0, columnspan=4, pady=10)\n", "\n", "# <PERSON><PERSON>\n", "gyg_var = tk.StringVar(value=str(default_gyg))\n", "finans_var = tk.StringVar(value=str(default_finans))\n", "hasar_var = tk.StringVar(value=str(default_hasar))\n", "kar_var = tk.StringVar(value=str(default_kar))\n", "\n", "ttk.Label(root3, text=\"GYG Oranı (%):\").grid(row=1, column=0, sticky=\"w\", padx=10, pady=5)\n", "ttk.Entry(root3, textvariable=gyg_var).grid(row=1, column=1, padx=10)\n", "\n", "ttk.Label(root3, text=\"Finansman Oranı (%):\").grid(row=2, column=0, sticky=\"w\", padx=10, pady=5)\n", "ttk.Entry(root3, textvariable=finans_var).grid(row=2, column=1, padx=10)\n", "\n", "ttk.Label(root3, text=\"<PERSON>ar <PERSON>anı (%):\").grid(row=3, column=0, sticky=\"w\", padx=10, pady=5)\n", "ttk.Entry(root3, textvariable=hasar_var).grid(row=3, column=1, padx=10)\n", "\n", "ttk.Label(root3, text=\"<PERSON><PERSON> (%):\").grid(row=4, column=0, sticky=\"w\", padx=10, pady=5)\n", "ttk.Entry(root3, textvariable=kar_var).grid(row=4, column=1, padx=10)\n", "\n", "# Maliyet kolonları\n", "maliyet_kolonlari = [\n", "    \"Maliyet_Şube\", \"Maliyet_Pickup_0\", \"Maliyet_Kira_0\", \"Maliyet_Elleçleme_0\",\n", "    \"Maliyet_Hat_1\", \"Maliyet_Kira_1\", \"Maliyet_Elleçleme_1\",\n", "    \"Maliyet_Hat_2\", \"Maliyet_Kira_2\", \"Maliyet_Elleçleme_2\",\n", "    \"Maliyet_Hat_3\", \"Maliyet_Kira_3\", \"Maliyet_Elleçleme_3\",\n", "    \"Maliyet_Dağıtım\"\n", "]\n", "\n", "# df_fiyat'ın hazır old<PERSON><PERSON><PERSON>u <PERSON>, numeric dönüşüm yapıyoruz\n", "for col in maliyet_kolonlari:\n", "    df_fiyat[col] = pd.to_numeric(df_fiyat[col], errors='coerce').fillna(0)\n", "\n", "df_fiyat[\"Toplam_Maliyet_TL\"] = df_fiyat[maliyet_kolonlari].sum(axis=1)\n", "df_fiyat[\"Toplam_Maliyet_Tutar\"] = df_fiyat[\"Toplam_Maliyet_TL\"] * df_fiyat[\"Desi\"].fillna(1)\n", "\n", "def varis_birimi(row):\n", "    il = str(row[\"Varış İl\"]).strip()\n", "    aktar = str(row[\"Varış Aktarma\"]).strip()\n", "    return f\"{il} - {aktar}\" if il.upper() == \"İSTANBUL\" else il\n", "\n", "df_fiyat[\"<PERSON><PERSON><PERSON><PERSON>_Birim\"] = df_fiyat.apply(varis_birimi, axis=1)\n", "\n", "# Desi toplamı\n", "desi_grp = df_fiyat.groupby(\"Varış_Birim\")[\"Desi\"].sum().reset_index()\n", "# Maliyet toplamı\n", "maliyet_grp = df_fiyat.groupby(\"Varış_Birim\")[\"Toplam_Maliyet_Tutar\"].sum().reset_index()\n", "\n", "# B<PERSON>leştir (merge)\n", "ozet_df = pd.merge(desi_grp, maliyet_grp, on=\"Varış_Birim\", how=\"outer\").fillna(0)\n", "\n", "# Treeview - <PERSON><PERSON><PERSON> oluşturma\n", "tree = ttk.Treeview(root3, columns=(\"varis\", \"desi\", \"maliyet\"), show='headings', height=15)\n", "tree.grid(row=5, column=0, columnspan=4, padx=10, pady=10)\n", "\n", "tree.heading(\"varis\", text=\"VARIŞ\")\n", "tree.heading(\"desi\", text=\"DESİ\")\n", "tree.heading(\"maliyet\", text=\"TL/DESİ\")\n", "\n", "tree.column(\"varis\", width=200, anchor='w')\n", "tree.column(\"desi\", width=100, anchor='center')\n", "tree.column(\"maliyet\", width=120, anchor='e')\n", "\n", "def guncelle_treeview():\n", "    tree.delete(*tree.get_children())\n", "    try:\n", "        gyg = float(gyg_var.get())\n", "        finans = float(finans_var.get())\n", "        hasar = float(hasar_var.get())\n", "        kar = float(kar_var.get())\n", "    except ValueError:\n", "        gyg = default_gyg\n", "        finans = default_finans\n", "        hasar = default_hasar\n", "        kar = default_kar\n", "\n", "    for _, row in ozet_df.iterrows():\n", "        varis = row[\"Varış_Birim\"]\n", "        desi = round(row[\"Desi\"])\n", "        maliyet = round(row[\"Toplam_Maliyet_Tutar\"]/row[\"Desi\"]*(1+(gyg+finans+hasar+kar)/100), 2)\n", "        tree.insert(\"\", tk.END, values=(varis, desi, maliyet))\n", "\n", "# Başlangıçta listeyi doldur\n", "guncelle_treeview()\n", "\n", "# <PERSON><PERSON> g<PERSON><PERSON> her değişiklikte güncelleme ekle\n", "def oran_degisti(*args):\n", "    guncelle_treeview()\n", "\n", "gyg_var.trace_add(\"write\", oran_degisti)\n", "finans_var.trace_add(\"write\", oran_degisti)\n", "hasar_var.trace_add(\"write\", oran_degisti)\n", "kar_var.trace_add(\"write\", oran_degisti)\n", "\n", "# Genel toplam hesaplama ve gösterme (altında)\n", "genel_toplam_frame = ttk.Frame(root3)\n", "genel_toplam_frame.grid(row=6, column=0, columnspan=4, pady=10)\n", "\n", "# Toplamları hesapla ve göster\n", "def genel_toplam_guncelle():\n", "    try:\n", "        gyg = float(gyg_var.get())\n", "        finans = float(finans_var.get())\n", "        hasar = float(hasar_var.get())\n", "        kar = float(kar_var.get())\n", "    except ValueError:\n", "        gyg = default_gyg\n", "        finans = default_finans\n", "        hasar = default_hasar\n", "        kar = default_kar\n", "\n", "    toplam_desi = ozet_df[\"Desi\"].sum()\n", "    toplam_maliyet = ozet_df[\"Toplam_Maliyet_Tutar\"].sum()\n", "    toplam_gyg = toplam_maliyet * gyg / 100\n", "    toplam_finans = toplam_maliyet * finans / 100\n", "    toplam_hasar = toplam_maliyet * hasar / 100\n", "    toplam_kar = toplam_maliyet * kar / 100\n", "    toplam_satis = toplam_maliyet + toplam_gyg + toplam_finans + toplam_hasar + toplam_kar\n", "    toplam_tl_desi = round(toplam_satis / toplam_desi, 2) if toplam_desi != 0 else 0\n", "\n", "    for widget in genel_toplam_frame.winfo_children():\n", "        widget.destroy()\n", "\n", "    headers = [\"TOPLAM\", \"DESİ\", \"MALİYET\", \"GYG\", \"FİNANS\", \"HASAR\", \"KAR\", \"SATIŞ\", \"SATIŞ/DESİ\"]\n", "    values = [\"\", round(toplam_desi, 2), round(toplam_maliyet, 2), round(toplam_gyg, 2), round(toplam_finans, 2),\n", "              round(toplam_hasar, 2), round(toplam_kar, 2), round(toplam_satis, 2), toplam_tl_desi]\n", "\n", "    for c, h in enumerate(headers):\n", "        ttk.Label(genel_toplam_frame, text=h, font=(\"Arial\", 9, \"bold\")).grid(row=0, column=c, padx=5)\n", "    for c, v in enumerate(values):\n", "        ttk.Label(genel_toplam_frame, text=v, font=(\"Arial\", 9)).grid(row=1, column=c, padx=5)\n", "\n", "genel_toplam_guncelle()\n", "\n", "# <PERSON><PERSON> g<PERSON><PERSON><PERSON> her değişiklikte toplamları da güncelle\n", "gyg_var.trace_add(\"write\", lambda *args: genel_toplam_guncelle())\n", "finans_var.trace_add(\"write\", lambda *args: genel_toplam_guncelle())\n", "hasar_var.trace_add(\"write\", lambda *args: genel_toplam_guncelle())\n", "kar_var.trace_add(\"write\", lambda *args: genel_toplam_guncelle())\n", "\n", "# <PERSON><PERSON> (oranları kaydedip pencereyi kapatır)\n", "ttk.Button(root3, text=\"<PERSON><PERSON> ve Kapat\", command=oranlari_kaydet_ve_kapat).grid(row=7, column=0, columnspan=4, pady=10)\n", "\n", "# Excel olarak kaydetme butonu\n", "ttk.Button(root3, text=\"Excel O<PERSON>ak <PERSON>pat\", command=excel_kaydet_ve_cikis).grid(row=8, column=0, columnspan=4, pady=10)\n", "\n", "root3.mainloop()\n", "\n", "# Oranlar çıktı\n", "# print(f\"GYG Oranı: {gyg_orani}%\")\n", "# print(f\"Finansman Oranı: {finans_orani}%\")\n", "# print(f\"Hasar Oranı: {hasar_orani}%\")\n", "# print(f\"Kar <PERSON>: {kar_orani}%\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}