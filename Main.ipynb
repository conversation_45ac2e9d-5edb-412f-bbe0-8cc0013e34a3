# BÖLÜM 0 GEREKLİ KÜTÜPHANELER✅
import pandas as pd
from itertools import product
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import tkinter.filedialog as fd


# BÖLÜM 0.1 GUİ PENCERESİ OLUŞTURMA VE VERİLERİ TOPLAMA

il_aktarma_dict = {
    "ADANA": ["ADANA"],
    "ADIYAMAN": ["GA<PERSON><PERSON>ANTEP"],
    "AFYONKARAHİSAR": ["AFY<PERSON>"],
    "AĞRI": ["ERZURUM", "VAN"],
    "AKSARAY": ["AKSARAY"],
    "AMASYA": ["MER<PERSON><PERSON><PERSON><PERSON>"],
    "ANKARA": ["AKSARAY", "<PERSON><PERSON><PERSON>"],
    "ANTALYA": ["<PERSON><PERSON><PERSON>Y<PERSON>", "FETH<PERSON>Y<PERSON>"],
    "ARDAHAN": ["ERZURUM"],
    "ARTVİN": ["TRABZON"],
    "AYDIN": ["DENİZ<PERSON><PERSON>", "<PERSON><PERSON>", "MUĞLA"],
    "BALIKESİR": ["BALIKESİR"],
    "BARTIN": ["DÜZ<PERSON>"],
    "BATMAN": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"],
    "<PERSON>YBURT": ["ERZURUM"],
    "BİLECİK": ["BURSA"],
    "BİNGÖL": ["ELAZIĞ"],
    "BİTLİS": ["ELAZIĞ"],
    "BOLU": ["DÜZCE"],
    "BURDUR": ["ANTALYA"],
    "BURSA": ["BURSA"],
    "ÇANAKKALE": ["ÇANAKKALE"],
    "ÇANKIRI": ["ANKARA"],
    "ÇORUM": ["MERZİFON"],
    "DENİZLİ": ["DENİZLİ"],
    "DİYARBAKIR": ["DİYARBAKIR"],
    "DÜZCE": ["DÜZCE"],
    "EDİRNE": ["BABAESKİ"],
    "ELAZIĞ": ["ELAZIĞ"],
    "ERZİNCAN": ["SİVAS"],
    "ERZURUM": ["ERZURUM"],
    "ESKİŞEHİR": ["ESKİŞEHİR"],
    "GAZİANTEP": ["GAZİANTEP"],
    "GİRESUN": ["SAMSUN"],
    "GÜMÜŞHANE": ["ERZURUM", "TRABZON"],
    "HAKKARİ": ["VAN"],
    "HATAY": ["HATAY"],
    "IĞDIR": ["VAN"],
    "ISPARTA": ["AFYON"],
    "İSTANBUL": ["ANADOLU", "AVRUPA"],
    "İZMİR": ["EGE", "İZMİR", "MANİSA"],
    "KAHRAMANMARAŞ": ["GAZİANTEP"],
    "KARABÜK": ["DÜZCE"],
    "KARAMAN": ["KONYA"],
    "KARS": ["ERZURUM"],
    "KASTAMONU": ["ANKARA"],
    "KAYSERİ": ["KAYSERİ"],
    "KIRIKKALE": ["ANKARA"],
    "KIRKLARELİ": ["BABAESKİ", "TRAKYA"],
    "KIRŞEHİR": ["AKSARAY", "KAYSERİ"],
    "KİLİS": ["GAZİANTEP"],
    "KOCAELİ": ["ANADOLU", "KOCAELİ"],
    "KONYA": ["AKSARAY", "KONYA"],
    "KÜTAHYA": ["AFYON"],
    "MALATYA": ["MALATYA"],
    "MANİSA": ["MANİSA"],
    "MARDİN": ["MARDİN"],
    "MERSİN": ["MERSİN"],
    "MUĞLA": ["BODRUM", "FETHİYE", "MUĞLA"],
    "MUŞ": ["ELAZIĞ"],
    "NEVŞEHİR": ["AKSARAY"],
    "NİĞDE": ["AKSARAY"],
    "ORDU": ["SAMSUN"],
    "OSMANİYE": ["ADANA"],
    "RİZE": ["TRABZON"],
    "SAKARYA": ["KOCAELİ"],
    "SAMSUN": ["MERZİFON", "SAMSUN"],
    "SİİRT": ["DİYARBAKIR"],
    "SİNOP": ["MERZİFON", "SAMSUN"],
    "SİVAS": ["SİVAS"],
    "ŞANLIURFA": ["GAZİANTEP", "ŞANLIURFA"],
    "ŞIRNAK": ["MARDİN"],
    "TEKİRDAĞ": ["BABAESKİ", "TRAKYA"],
    "TOKAT": ["MERZİFON"],
    "TRABZON": ["TRABZON"],
    "TUNCELİ": ["ELAZIĞ"],
    "UŞAK": ["AFYON"],
    "VAN": ["VAN"],
    "YALOVA": ["BURSA"],
    "YOZGAT": ["KAYSERİ"],
    "ZONGULDAK": ["DÜZCE"]
}

def aktarma_guncelle(event):
    secilen_il = secim_cıkıs_il_var.get()
    aktarmalar = il_aktarma_dict.get(secilen_il, [])
    secim_cıkıs_aktarma_var.set('')
    aktarma_combo['values'] = aktarmalar
    if aktarmalar:
        aktarma_combo.current(0)
    else:
        aktarma_combo.set('')

def kaydet_ve_kapat():
    global secim, secim_ay, secim_yıl, secim_maliyet, secim_sube_maliyet, secim_pickup_maliyet, secim_cıkıs_aktarma, secim_cıkıs_il
    secim = secim_var.get()
    secim_ay = secim_ay_var.get()
    secim_yıl = secim_yıl_var.get()
    secim_maliyet = secim_maliyet_var.get()
    try:
        secim_sube_maliyet = float(secim_sube_maliyet_var.get())
    except ValueError:
        secim_sube_maliyet = 1.0
    try:
        secim_pickup_maliyet = float(secim_pickup_maliyet_var.get())
    except ValueError:
        secim_pickup_maliyet = 1.0
    secim_cıkıs_il = secim_cıkıs_il_var.get()
    secim_cıkıs_aktarma = secim_cıkıs_aktarma_var.get()
    root.destroy()

# GUI başlat
root = tk.Tk()
root.title("Seçim Ekranı")

# Tk değişkenleri
secim_var = tk.StringVar(value="İL")
secim_ay_var = tk.StringVar(value="OCAK")
secim_yıl_var = tk.StringVar(value="2025")
secim_maliyet_var = tk.StringVar(value="DAĞITIM")
secim_sube_maliyet_var = tk.StringVar(value="0.0")
secim_pickup_maliyet_var = tk.StringVar(value="0.0")
secim_cıkıs_aktarma_var = tk.StringVar()
secim_cıkıs_il_var = tk.StringVar()

# Etiket ve bileşenler
ttk.Label(root, text="Seçim (İL/İLÇE):").grid(row=0, column=0, sticky="w", padx=5, pady=5)
ttk.Combobox(root, textvariable=secim_var, values=["İL", "İLÇE"]).grid(row=0, column=1, padx=5, pady=5)

ttk.Label(root, text="Seçim Ay:").grid(row=1, column=0, sticky="w", padx=5, pady=5)
ttk.Combobox(root, textvariable=secim_ay_var, values=["ALL", "OCAK", "ŞUBAT", "MART", "NİSAN", "MAYIS", "HAZİRAN", "TEMMUZ", "AĞUSTOS", "EYLÜL", "EKİM", "KASIM", "ARALIK"]).grid(row=1, column=1, padx=5, pady=5)

ttk.Label(root, text="Seçim Yıl:").grid(row=2, column=0, sticky="w", padx=5, pady=5)
ttk.Combobox(root, textvariable=secim_yıl_var, values=["2025", "2026", "2027"]).grid(row=2, column=1, padx=5, pady=5)

ttk.Label(root, text="Seçim Maliyet:").grid(row=3, column=0, sticky="w", padx=5, pady=5)
ttk.Combobox(root, textvariable=secim_maliyet_var, values=["ALL", "DAĞITIM", "PALET TAŞIMA", "ZİNCİR MAĞAZA"]).grid(row=3, column=1, padx=5, pady=5)

ttk.Label(root, text="Seçim Şube Maliyet:").grid(row=4, column=0, sticky="w", padx=5, pady=5)
ttk.Entry(root, textvariable=secim_sube_maliyet_var).grid(row=4, column=1, padx=5, pady=5)

ttk.Label(root, text="Seçim Pickup Maliyet:").grid(row=5, column=0, sticky="w", padx=5, pady=5)
ttk.Entry(root, textvariable=secim_pickup_maliyet_var).grid(row=5, column=1, padx=5, pady=5)

ttk.Label(root, text="Seçim Çıkış İl:").grid(row=6, column=0, sticky="w", padx=5, pady=5)
il_combo = ttk.Combobox(root, textvariable=secim_cıkıs_il_var, values=sorted(il_aktarma_dict.keys()))
il_combo.grid(row=6, column=1, padx=5, pady=5)
il_combo.bind("<<ComboboxSelected>>", aktarma_guncelle)

ttk.Label(root, text="Seçim Çıkış Aktarma:").grid(row=7, column=0, sticky="w", padx=5, pady=5)
aktarma_combo = ttk.Combobox(root, textvariable=secim_cıkıs_aktarma_var)
aktarma_combo.grid(row=7, column=1, padx=5, pady=5)

ttk.Button(root, text="Kaydet ve Devam Et", command=kaydet_ve_kapat).grid(row=8, column=0, columnspan=2, pady=10)

# GUI çalıştır
root.mainloop()

# BÖLÜM 0.2 GUİ PENCERESİ OLUŞTURMA VE EXCEL YÜKLEME


# Global değişkenler
excel_yolu = ""
excel_yolu_desi = ""
df_maliyetler = None
df_desi = None
df_toplama = None
df_kırılım = None
df_hat = None

def dosya_sec(entry_var):
    yol = filedialog.askopenfilename(filetypes=[("Excel Dosyası", "*.xlsx")])
    if yol:
        entry_var.set(yol)

def desi_durumu_degisti():
    if desi_checkbox_var.get():
        desi_yolu_entry.configure(state='normal')
        desi_sec_button.configure(state='normal')
    else:
        desi_yolu_entry.configure(state='disabled')
        desi_sec_button.configure(state='disabled')

def verileri_yukle_ve_devam():
    global excel_yolu, excel_yolu_desi, df_maliyetler, df_desi, df_toplama, df_kırılım, df_hat

    yol_maliyet = maliyet_yolu_var.get()
    if not yol_maliyet:
        messagebox.showerror("Hata", "Lütfen maliyet dosyasını seçin.")
        return

    try:
        df = pd.read_excel(yol_maliyet, sheet_name=None)
        df_maliyetler = df["TÜMÜ"]
        df_toplama = df["toplama_elleçleme"]
        df_kırılım = df["kırılım"]
        df_hat = df["kırılım_hat"]
        excel_yolu = yol_maliyet

        if desi_checkbox_var.get():
            yol_desi = desi_yolu_var.get()
            if not yol_desi:
                messagebox.showerror("Hata", "Lütfen desi dosyasını seçin.")
                return
            df_desi = pd.read_excel(yol_desi)
            excel_yolu_desi = yol_desi
        else:
            df_desi = df["desi"]

    except Exception as e:
        messagebox.showerror("Dosya Hatası", f"Excel dosyası okunamadı:\n{e}")
        return

    root2.destroy()
    #print("📁 Veriler başarıyla yüklendi.")
    #print(f"Maliyet Dosyası: {excel_yolu}")
    #print(f"Desi Dosyası: {excel_yolu_desi if desi_checkbox_var.get() else '(ilk excelden geldi)'}")

# Yeni pencere
root2 = tk.Tk()
root2.title("Bölüm 2 - Excel Yükleme")

# Değişkenler
maliyet_yolu_var = tk.StringVar()
desi_yolu_var = tk.StringVar()
desi_checkbox_var = tk.BooleanVar()

# Maliyet Dosyası
ttk.Label(root2, text="Maliyet Excel Dosyası:\n Birim Maliyetleri 2025 Liste.xlsx dosyasına göre hazırlanmıştır.").grid(row=0, column=0, sticky="w", padx=5, pady=5)
ttk.Entry(root2, textvariable=maliyet_yolu_var, width=50).grid(row=0, column=1, padx=5, pady=5)
ttk.Button(root2, text="Gözat", command=lambda: dosya_sec(maliyet_yolu_var)).grid(row=0, column=2, padx=5, pady=5)

# Desi Ayrı Dosya Seçimi
ttk.Checkbutton(root2, text="Desi ayrı dosyada\n Eğer ayrıca desi girmeyecekseni burayı boş bırakın.\n Sekme Adı 'desi' olmalı ve sütunlar sırasıyla 'Dönem', 'Çıkış İl', 'Çıkış Aktarma', 'Varış İl', 'Varış Aktarma', 'Toplam Desi' olmalı.", variable=desi_checkbox_var, command=desi_durumu_degisti).grid(row=1, column=0, columnspan=2, sticky="w", padx=5, pady=5)

# Desi Dosyası
ttk.Label(root2, text="Desi Excel Dosyası:").grid(row=2, column=0, sticky="w", padx=5, pady=5)
desi_yolu_entry = ttk.Entry(root2, textvariable=desi_yolu_var, width=50, state='disabled')
desi_yolu_entry.grid(row=2, column=1, padx=5, pady=5)

desi_sec_button = ttk.Button(root2, text="Gözat", command=lambda: dosya_sec(desi_yolu_var), state='disabled')
desi_sec_button.grid(row=2, column=2, padx=5, pady=5)

# Kaydet butonu
ttk.Button(root2, text="Yükle ve Devam Et", command=verileri_yukle_ve_devam).grid(row=3, column=0, columnspan=3, pady=15)

root2.mainloop()

# BÖLÜM 1 VE 2 KALDIRILDI GUİ PENCERESİ OLUŞTURULDU!!!


#_________________________________________________________________________________
# BÖLÜM 1 GEREKLİ BİLGİLER✅
# secim = "İL"    # "İlçe", "İl"  ?aktarma kullanmak mantıklı değil
# secim_ay = "OCAK"   # "Ocak", "Şubat", "Mart"
# secim_yıl = "2025"  # "2025", "2026", "2027"
# secim_maliyet = "DAĞITIM"    # "Dağıtım", "PALET TAŞIMA", "ZİNCİR MAĞAZA"
# secim_sube_maliyet = 1.2    # hesapladığı maliyete buraya eklenir
# secim_cıkıs_aktarma = "ELAZIĞ"
# secim_cıkıs_il = "ELAZIĞ"

#_________________________________________________________________________________
# BÖLÜM 2 VERİ OKUMA✅
# excel_yolu = r"C:\Users\<USER>\OneDrive - Horoz Lojistik\YID Satış\Birim Fiyat Çalışmaları\2025 Yılı Birim Fiyatlar\Birim Maliyetleri 2025 Liste.xlsx"

# Tüm sayfaları tek seferde oku
# df = pd.read_excel(excel_yolu, sheet_name=None)

# Toplama/Elleçleme verileri
# df_maliyetler = df["TÜMÜ"]

# Toplama/Elleçleme verileri
# df_desi = df["desi"]

# Toplama/Elleçleme verileri
# df_toplama = df["toplama_elleçleme"]

# Hat verileri
# df_hat = df["kırılım_hat"]


#_BÖLÜM 3 DATAFRAME OLUŞTURMA✅

secim_sutunlari = {"AKTARMA": ["Aktarma"],"İL": ["İl", "Aktarma"],"İLÇE": ["İlçe", "İl", "Aktarma"]} # Seçime göre sütunları belirle
kombinasyonlar = df_kırılım[secim_sutunlari[secim]].drop_duplicates().values.tolist() # Kombinasyonları oluştur
carpaz_kombinasyonlar = list(product(kombinasyonlar, kombinasyonlar))
# Kolon isimlerini tanımlama
headers = {"İLÇE":['Çıkış İlçe', 'Çıkış İl', 'Çıkış Aktarma','Varış İlçe', 'Varış İl', 'Varış Aktarma'],
            "İL":['Çıkış İl', 'Çıkış Aktarma','Varış İl', 'Varış Aktarma'],
            "AKTARMA":['Çıkış Aktarma','Varış Aktarma']}

# DataFrame'e dönüştürme ve Desi sütunu ekleme
df_fiyat = pd.DataFrame(
    [list(sum(pair, [])) for pair in carpaz_kombinasyonlar],
    columns=headers[secim]
)
# print(f"\nFiyat Çalışması {secim} bazlı oluşturuldu. Toplam {len(df_fiyat)} satır.")

# BÖLÜM 4 İLAVE SÜTUNLARIN EKLENMESİ✅

# tTOPLAMA VE ELLEÇLEME
df_fiyat = pd.merge(
    df_fiyat,
    df_toplama[['Çıkış İl', 'Çıkış Aktarma', 'Çıkış Toplama', 'Çıkış Elleçleme']],
    on=['Çıkış İl', 'Çıkış Aktarma'],
    how='left'
)

# HATLAR
df_fiyat = pd.merge(
    df_fiyat,
    df_hat[['Çıkış Aktarma', 'Varış Aktarma', 'Hat1', 'Hat2', 'Hat3']],
    on=['Çıkış Aktarma', 'Varış Aktarma'],
    how='left'
)

# BÖLÜM 5 MALİYET TÜRÜNE GÖRE TERİMLERİN BELİRLENMESİ

# Maliyet türüne göre terim eşleştirmesi
def get_maliyet_terimleri(maliyet_turu):
    """Maliyet türüne göre kullanılacak terimleri döndürür"""
    terimler = {
        'KİRA': 'KİRA',
        'HAT': 'HAT'
    }
    
    if maliyet_turu == "DAĞITIM":
        terimler.update({
            'DAĞITIM': 'DAĞITIM',
            'TOPLAMA': 'TOPLAMA',
            'ELLEÇLEME': 'ELLEÇLEME'
        })
    elif maliyet_turu == "PALET TAŞIMA":
        terimler.update({
            'DAĞITIM': 'PALET TAŞIMA',
            'TOPLAMA': 'PALET TAŞIMA',
            'ELLEÇLEME': 'PALET ELLEÇLEME'
        })
    elif maliyet_turu == "ZİNCİR MAĞAZA":
        terimler.update({
            'DAĞITIM': 'ZİNCİR MAĞAZA',
            'TOPLAMA': 'ZİNCİR MAĞAZA',
            'ELLEÇLEME': 'ELLEÇLEME'
        })
    
    return terimler
terimler = get_maliyet_terimleri(secim_maliyet)


# BÖLÜM 5.2 DESİ VE ŞUBE MALİYETİ HESAPLANMASI

# 👉DESİ👈
df_desi_filtered = df_desi[df_desi['Dönem'] == secim_ay]
df_fiyat = df_fiyat.merge(
    df_desi_filtered[['Çıkış İl', 'Çıkış Aktarma', 'Varış Aktarma', 'Varış İl', 'Toplam Desi']],
    on=['Çıkış İl', 'Çıkış Aktarma', 'Varış Aktarma', 'Varış İl'],
    how='left'
)

df_fiyat['Desi'] = df_fiyat['Toplam Desi'].fillna(1)
df_fiyat.drop(columns='Toplam Desi', inplace=True)  # geçici sütun silinir



# 👉ŞUBE MALİYETİ👈
df_fiyat['Maliyet_Şube'] = secim_sube_maliyet

# BÖLÜM 6 MALİYETLERİN İFADE EDİLMESİ

# 👉ÇIKIŞ 👈
df_fiyat['Maliyet_Pickup_0'] = f"{secim_yıl},{secim_ay},{terimler['TOPLAMA']}," + df_fiyat['Çıkış Toplama'].fillna('') + "," + df_fiyat.apply(lambda row: row['Çıkış İl'] if pd.notna(row['Çıkış İl']) and row['Çıkış Aktarma'] == 'ELAZIĞ' else '', axis=1).fillna('')

df_fiyat['Maliyet_Kira_0'] = df_fiyat.apply(
    lambda row: (
        f"{secim_yıl},{secim_ay},{terimler['KİRA']},{row['Çıkış Toplama'] or ''},"
        if (row['Çıkış Toplama'] == row['Çıkış Elleçleme'] or pd.isna(row['Çıkış Elleçleme']))
        else f"{secim_yıl},{secim_ay},{terimler['KİRA']},{row['Çıkış Toplama'] or ''},+{secim_yıl},{secim_ay},{terimler['KİRA']},{row['Çıkış Elleçleme'] or ''},"
    ),
    axis=1
)

df_fiyat['Maliyet_Elleçleme_0'] = df_fiyat['Çıkış Elleçleme'].apply(
    lambda val: f"{secim_yıl},{secim_ay},{terimler['ELLEÇLEME']},{val}," if pd.notna(val) else ''
)

df_fiyat['Maliyet_Hat_1'] = df_fiyat.apply(
    lambda row: (
        f"{secim_yıl},{secim_ay},{terimler['HAT']}," +
        (
            row['Çıkış Elleçleme'] if pd.notna(row['Çıkış Elleçleme']) and row['Çıkış Elleçleme'] != ''
            else row['Çıkış Aktarma'] or ''
        ) +
        f",{row['Hat1']}"
        if pd.notna(row['Hat1']) and row['Hat1'] != ''
        else ''
    ),
    axis=1
)

# 👉ARA 1👈
df_fiyat['Maliyet_Kira_1'] = df_fiyat.apply(
    lambda row: (
        f"{secim_yıl},{secim_ay},{terimler['KİRA']},{row['Hat1']},"
        if pd.notna(row['Hat2']) and row['Hat2'] != ''
        else ''
    ),
    axis=1
)

df_fiyat['Maliyet_Elleçleme_1'] = df_fiyat.apply(
    lambda row: (
        f"{secim_yıl},{secim_ay},{terimler['ELLEÇLEME']},{row['Hat1']},"
        if pd.notna(row['Hat2']) and row['Hat2'] != ''
        else ''
    ),
    axis=1
)

df_fiyat['Maliyet_Hat_2'] = df_fiyat.apply(
    lambda row: (
        f"{secim_yıl},{secim_ay},{terimler['HAT']},{row['Hat1'] or ''},{row['Hat2']}"
        if pd.notna(row['Hat2']) and row['Hat2'] != ''
        else ''
    ),
    axis=1
)

# 👉ARA 2👈
df_fiyat['Maliyet_Kira_2'] = df_fiyat.apply(
    lambda row: (
        f"{secim_yıl},{secim_ay},{terimler['KİRA']},{row['Hat2']},"
        if pd.notna(row['Hat3']) and row['Hat3'] != ''
        else ''
    ),
    axis=1
)

df_fiyat['Maliyet_Elleçleme_2'] = df_fiyat.apply(
    lambda row: (
        f"{secim_yıl},{secim_ay},{terimler['ELLEÇLEME']},{row['Hat2']},"
        if pd.notna(row['Hat3']) and row['Hat3'] != ''
        else ''
    ),
    axis=1
)

df_fiyat['Maliyet_Hat_3'] = df_fiyat.apply(
    lambda row: (
        f"{secim_yıl},{secim_ay},{terimler['HAT']},{row['Hat2'] or ''},{row['Hat3']}"
        if pd.notna(row['Hat3']) and row['Hat3'] != ''
        else ''
    ),
    axis=1
)

# 👉VARIŞ👈
df_fiyat['Maliyet_Kira_3'] = df_fiyat.apply(
    lambda row: (
        f"{secim_yıl},{secim_ay},{terimler['KİRA']},{row['Varış Aktarma']}," if pd.notna(row['Hat1']) and row['Hat1'] != '' and pd.notna(row['Varış Aktarma']) else ''),
    axis=1
)

df_fiyat['Maliyet_Elleçleme_3'] = df_fiyat.apply(
    lambda row: (
        f"{secim_yıl},{secim_ay},{terimler['ELLEÇLEME']},{row['Varış Aktarma'] or ''},{row['Varış İl'] if row['Varış Aktarma'] == 'ELAZIĞ' else ''}" if pd.notna(row['Hat1']) and row['Hat1'] != '' and pd.notna(row['Varış Aktarma']) else ''),
    axis=1
)

df_fiyat['Maliyet_Dağıtım'] = df_fiyat.apply(
    lambda row: f"{secim_yıl},{secim_ay},{terimler['DAĞITIM']},{row['Varış Aktarma'] or ''},{row['Varış İl'] if row['Varış Aktarma'] == 'ELAZIĞ' else ''}",
    axis=1
)


# BÖLÜM 7 OLUŞTURULAN DATAFRAME FİLTRELENMESİ VE ELLEÇLEMENİN TEMİZLENMESİ


# Elleçleme sütunlarını tanımla
ellecleme_sutunlari = ['Maliyet_Elleçleme_0', 'Maliyet_Elleçleme_1', 'Maliyet_Elleçleme_2', 'Maliyet_Elleçleme_3']

# Kontrol edilecek anahtar kelimeler
anahtar_kelimeler = ['ANADOLU', 'AVRUPA', 'DİAMOND','HADIMKÖY YID']

# Her bir Elleçleme sütunu için işlem yap
for sutun in ellecleme_sutunlari:
    if sutun in df_fiyat.columns:
        df_fiyat[sutun] = df_fiyat.apply(
            lambda row: row[sutun] if (isinstance(row[sutun], str) and 
                                     any(kelime in row[sutun] for kelime in anahtar_kelimeler) and
                                     not ('DİAMOND' in row[sutun] and row['Çıkış Aktarma'] == row['Varış Aktarma']))
                          else '',
            axis=1
        )
    else:
        print(f"Uyarı: {sutun} sütunu DataFrame'de bulunamadı")

# CSV olarak dışla aktar türkçe karakterli, sütunlar arasında "$" koy   ✅seçilen çıkışa göre maliyetler
df_fiyat.to_csv(f"fiyat_calisma_A.csv", index=False, encoding='utf-8-sig', sep='$')
#print("CSV dosyası oluşturuldu.")


# Filtreleme işlemi
df_fiyat = df_fiyat[
    (df_fiyat['Çıkış İl'] == secim_cıkıs_il) & 
    (df_fiyat['Çıkış Aktarma'] == secim_cıkıs_aktarma)
].copy()
# Filtreleme sonucu kontrol
if df_fiyat.empty:
    raise ValueError("Filtreleme sonucu hiç kayıt bulunamadı! Girilen İl ve Aktarma değerini kontrol edin!")


# Sonucu kontrol
# print("Elleçleme sütunları temizlendi. İlk 5 satır:")
# display(df_fiyat.head())

#BÖLÜM 8 MALİYETLERİ TL'YE ÇEVİRME

# Maliyet sütunlarının listesi (orijinal sütun isimleriyle)
maliyet_sutunlari = [
    'Maliyet_Pickup_0',
    'Maliyet_Kira_0',
    'Maliyet_Elleçleme_0',
    'Maliyet_Hat_1',
    'Maliyet_Kira_1',
    'Maliyet_Elleçleme_1',
    'Maliyet_Hat_2',
    'Maliyet_Kira_2',
    'Maliyet_Elleçleme_2',
    'Maliyet_Hat_3',
    'Maliyet_Kira_3',
    'Maliyet_Elleçleme_3',
    'Maliyet_Dağıtım'
]

# df_maliyetler'de arama yapmak için dictionary oluştur (daha hızlı erişim için)
maliyet_sozlugu = df_maliyetler.set_index('Uniq')['TL/DESİ'].to_dict()

def maliyeti_guncelle(deger):
    """Maliyet değerini TL karşılığı ile değiştirir (tekli veya ikili giriş olabilir)"""
    if pd.isna(deger) or not str(deger).strip():
        return None  # Boş veya NaN
    
    toplam_tl = 0.0
    parcalar = str(deger).split('+')  # '+' varsa ikiye böler, yoksa tek elemanlı olur
    
    for parca in parcalar:
        parca = parca.strip()  # Baş/son boşluk ve sondaki virgülü temizle  .rstrip(',')
        if not parca:
            continue
        tl_karsiligi = maliyet_sozlugu.get(parca)
        if tl_karsiligi is not None:
            toplam_tl += tl_karsiligi
        else:
            print(f"Uyarı: Eşleşme bulunamadı - {parca}")
    
    return toplam_tl if toplam_tl > 0 else None

# Her maliyet sütununu güncelle (yeni sütun oluşturmadan doğrudan güncelle)
for sutun in maliyet_sutunlari:
    if sutun in df_fiyat.columns:
        df_fiyat[sutun] = df_fiyat[sutun].apply(maliyeti_guncelle)
    else:
        print(f"Uyarı: {sutun} sütunu DataFrame'de bulunamadı")

df_fiyat['Maliyet_Pickup_0']


# pickup_maliyet girildi ise ve 0'dan büyük ise bu değer yeni pickup değeridir. aksi halde excelden hesaplanan getirilir.
try:
    pickup_maliyet = float(secim_pickup_maliyet)
except (ValueError, TypeError):
    pickup_maliyet = 0.0

if pickup_maliyet > 0:
    df_fiyat['Maliyet_Pickup_0'] = pickup_maliyet


# Sonuçları kontrol et
print("Maliyet dönüşümü tamamlandı. İlk 5 satır:")
display(df_fiyat.head())

# CSV olarak dışla aktar türkçe karakterli, sütunlar arasında "$" koy   ✅seçilen çıkışa göre maliyetler
df_fiyat.to_csv(f"fiyat_calisma_B.csv", index=False, encoding='utf-8-sig', sep='$')
#print("CSV dosyası oluşturuldu.")

# ÖZET EKRANI OLUŞTURULMASI VE ORANLARIN GİRİLMESİ

# Varsayılan oranlar
default_gyg = 3.58
default_finans = 4.0
default_hasar = 1.0
default_kar = 10.0

gyg_orani = default_gyg
finans_orani = default_finans
hasar_orani = default_hasar
kar_orani = default_kar

def oranlari_kaydet_ve_kapat():
    global gyg_orani, finans_orani, hasar_orani, kar_orani
    try:
        gyg_orani = float(gyg_var.get())
        finans_orani = float(finans_var.get())
        hasar_orani = float(hasar_var.get())
        kar_orani = float(kar_var.get())
    except ValueError:
        gyg_orani = default_gyg
        finans_orani = default_finans
        hasar_orani = default_hasar
        kar_orani = default_kar
    root3.destroy()

def excel_kaydet_ve_cikis():
    # Dosya kaydetme penceresi aç
    file_path = fd.asksaveasfilename(
        defaultextension=".xlsx",
        filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
        title="Özet Tablosunu Excel Olarak Kaydet"
    )
    if not file_path:
        return  # kullanıcı iptal ettiyse işlem yok

    # Oranları al (güncel değerler)
    try:
        gyg = float(gyg_var.get())
        finans = float(finans_var.get())
        hasar = float(hasar_var.get())
        kar = float(kar_var.get())
    except ValueError:
        gyg = default_gyg
        finans = default_finans
        hasar = default_hasar
        kar = default_kar

    # Detay DataFrame
    detay_df = ozet_df.copy()

    detay_df["Maliyet_TL/Desi"] = (detay_df["Toplam_Maliyet_Tutar"] / detay_df["Desi"])
    detay_df["Maliyet_TL/Desi"] = detay_df["Maliyet_TL/Desi"].round(2)

    detay_df["Satış_TL/Desi"] = (detay_df["Toplam_Maliyet_Tutar"] / detay_df["Desi"]) * (1 + (gyg + finans + hasar + kar) / 100)
    detay_df["Satış_TL/Desi"] = detay_df["Satış_TL/Desi"].round(2)

    # Genel toplam dataframe
    toplam_desi = detay_df["Desi"].sum()
    toplam_maliyet = detay_df["Toplam_Maliyet_Tutar"].sum()
    toplam_gyg = toplam_maliyet * gyg / 100
    toplam_finans = toplam_maliyet * finans / 100
    toplam_hasar = toplam_maliyet * hasar / 100
    toplam_kar = toplam_maliyet * kar / 100
    toplam_satis = toplam_maliyet + toplam_gyg + toplam_finans + toplam_hasar + toplam_kar
    toplam_tl_desi = round(toplam_satis / toplam_desi, 2) if toplam_desi != 0 else 0

    genel_toplam_dict = {
        "DESİ TOPLAM": [round(toplam_desi, 2)],
        "MALİYET TOPLAM": [round(toplam_maliyet, 2)],
        "GYG": [round(toplam_gyg, 2)],
        "FİNANS": [round(toplam_finans, 2)],
        "HASAR": [round(toplam_hasar, 2)],
        "KAR": [round(toplam_kar, 2)],
        "SATIŞ": [round(toplam_satis, 2)],
        "SATIŞ/DESİ": [toplam_tl_desi]
    }
    genel_toplam_df = pd.DataFrame(genel_toplam_dict)

    # Temel bilgiler dataframe
    temel_bilgiler = {
        #"Parametre": ["secim_yıl", "secim_ay", "secim_maliyet", "secim_cıkıs_il", "secim_cıkıs_aktarma","gyg_orani", "finans_orani", "hasar_orani", "kar_orani"],
        "Parametre": ["Yıl", "Ay", "Maliyet", "Çıkış İl", "Çıkış Aktarma","Gyg", "Finans", "Hasar", "Kar"],
        "Değer": [secim_yıl, secim_ay, secim_maliyet, secim_cıkıs_il, secim_cıkıs_aktarma,gyg, finans, hasar, kar]
    }
    temel_bilgiler_df = pd.DataFrame(temel_bilgiler)

    # Excel'e yaz
    with pd.ExcelWriter(file_path) as writer:
        # Sütun adlarını uygun şekilde değiştir
        detay_df_rename = detay_df.rename(columns={
            "Varış_Birim": "Varış",
            "Desi": "DESİ",
            "Toplam_Maliyet_Tutar": "Maliyet Toplam"
        })
        detay_df_rename[["Varış", "DESİ", "Maliyet_TL/Desi", "Satış_TL/Desi"]].to_excel(writer, index=False, sheet_name="Detay")
        genel_toplam_df.to_excel(writer, index=False, sheet_name="Genel Toplam")
        temel_bilgiler_df.to_excel(writer, index=False, sheet_name="Temel Bilgiler")

    root3.destroy()



root3 = tk.Tk()
root3.title("Özet ve Oran Girişi")

# Başlık
ozet_baslik = f"{secim_yıl}, {secim_ay}, {secim_maliyet} Fiyat Çalışması \nÇıkış İl {secim_cıkıs_il}, \nÇıkış Aktarma {secim_cıkıs_aktarma}"
ttk.Label(root3, text=ozet_baslik, font=("Arial", 12, "bold")).grid(row=0, column=0, columnspan=4, pady=10)

# Oran girişleri
gyg_var = tk.StringVar(value=str(default_gyg))
finans_var = tk.StringVar(value=str(default_finans))
hasar_var = tk.StringVar(value=str(default_hasar))
kar_var = tk.StringVar(value=str(default_kar))

ttk.Label(root3, text="GYG Oranı (%):").grid(row=1, column=0, sticky="w", padx=10, pady=5)
ttk.Entry(root3, textvariable=gyg_var).grid(row=1, column=1, padx=10)

ttk.Label(root3, text="Finansman Oranı (%):").grid(row=2, column=0, sticky="w", padx=10, pady=5)
ttk.Entry(root3, textvariable=finans_var).grid(row=2, column=1, padx=10)

ttk.Label(root3, text="Hasar Oranı (%):").grid(row=3, column=0, sticky="w", padx=10, pady=5)
ttk.Entry(root3, textvariable=hasar_var).grid(row=3, column=1, padx=10)

ttk.Label(root3, text="Kar Oranı (%):").grid(row=4, column=0, sticky="w", padx=10, pady=5)
ttk.Entry(root3, textvariable=kar_var).grid(row=4, column=1, padx=10)

# Maliyet kolonları
maliyet_kolonlari = [
    "Maliyet_Şube", "Maliyet_Pickup_0", "Maliyet_Kira_0", "Maliyet_Elleçleme_0",
    "Maliyet_Hat_1", "Maliyet_Kira_1", "Maliyet_Elleçleme_1",
    "Maliyet_Hat_2", "Maliyet_Kira_2", "Maliyet_Elleçleme_2",
    "Maliyet_Hat_3", "Maliyet_Kira_3", "Maliyet_Elleçleme_3",
    "Maliyet_Dağıtım"
]

# df_fiyat'ın hazır olduğunu varsayıyoruz, numeric dönüşüm yapıyoruz
for col in maliyet_kolonlari:
    df_fiyat[col] = pd.to_numeric(df_fiyat[col], errors='coerce').fillna(0)

df_fiyat["Toplam_Maliyet_TL"] = df_fiyat[maliyet_kolonlari].sum(axis=1)
df_fiyat["Toplam_Maliyet_Tutar"] = df_fiyat["Toplam_Maliyet_TL"] * df_fiyat["Desi"].fillna(1)

def varis_birimi(row):
    il = str(row["Varış İl"]).strip()
    aktar = str(row["Varış Aktarma"]).strip()
    return f"{il} - {aktar}" if il.upper() == "İSTANBUL" else il

df_fiyat["Varış_Birim"] = df_fiyat.apply(varis_birimi, axis=1)

# Desi toplamı
desi_grp = df_fiyat.groupby("Varış_Birim")["Desi"].sum().reset_index()
# Maliyet toplamı
maliyet_grp = df_fiyat.groupby("Varış_Birim")["Toplam_Maliyet_Tutar"].sum().reset_index()

# Birleştir (merge)
ozet_df = pd.merge(desi_grp, maliyet_grp, on="Varış_Birim", how="outer").fillna(0)

# Treeview - Tablo oluşturma
tree = ttk.Treeview(root3, columns=("varis", "desi", "maliyet"), show='headings', height=15)
tree.grid(row=5, column=0, columnspan=4, padx=10, pady=10)

tree.heading("varis", text="VARIŞ")
tree.heading("desi", text="DESİ")
tree.heading("maliyet", text="TL/DESİ")

tree.column("varis", width=200, anchor='w')
tree.column("desi", width=100, anchor='center')
tree.column("maliyet", width=120, anchor='e')

def guncelle_treeview():
    tree.delete(*tree.get_children())
    try:
        gyg = float(gyg_var.get())
        finans = float(finans_var.get())
        hasar = float(hasar_var.get())
        kar = float(kar_var.get())
    except ValueError:
        gyg = default_gyg
        finans = default_finans
        hasar = default_hasar
        kar = default_kar

    for _, row in ozet_df.iterrows():
        varis = row["Varış_Birim"]
        desi = round(row["Desi"])
        maliyet = round(row["Toplam_Maliyet_Tutar"]/row["Desi"]*(1+(gyg+finans+hasar+kar)/100), 2)
        tree.insert("", tk.END, values=(varis, desi, maliyet))

# Başlangıçta listeyi doldur
guncelle_treeview()

# Oran girişlerine her değişiklikte güncelleme ekle
def oran_degisti(*args):
    guncelle_treeview()

gyg_var.trace_add("write", oran_degisti)
finans_var.trace_add("write", oran_degisti)
hasar_var.trace_add("write", oran_degisti)
kar_var.trace_add("write", oran_degisti)

# Genel toplam hesaplama ve gösterme (altında)
genel_toplam_frame = ttk.Frame(root3)
genel_toplam_frame.grid(row=6, column=0, columnspan=4, pady=10)

# Toplamları hesapla ve göster
def genel_toplam_guncelle():
    try:
        gyg = float(gyg_var.get())
        finans = float(finans_var.get())
        hasar = float(hasar_var.get())
        kar = float(kar_var.get())
    except ValueError:
        gyg = default_gyg
        finans = default_finans
        hasar = default_hasar
        kar = default_kar

    toplam_desi = ozet_df["Desi"].sum()
    toplam_maliyet = ozet_df["Toplam_Maliyet_Tutar"].sum()
    toplam_gyg = toplam_maliyet * gyg / 100
    toplam_finans = toplam_maliyet * finans / 100
    toplam_hasar = toplam_maliyet * hasar / 100
    toplam_kar = toplam_maliyet * kar / 100
    toplam_satis = toplam_maliyet + toplam_gyg + toplam_finans + toplam_hasar + toplam_kar
    toplam_tl_desi = round(toplam_satis / toplam_desi, 2) if toplam_desi != 0 else 0

    for widget in genel_toplam_frame.winfo_children():
        widget.destroy()

    headers = ["TOPLAM", "DESİ", "MALİYET", "GYG", "FİNANS", "HASAR", "KAR", "SATIŞ", "SATIŞ/DESİ"]
    values = ["", round(toplam_desi, 2), round(toplam_maliyet, 2), round(toplam_gyg, 2), round(toplam_finans, 2),
              round(toplam_hasar, 2), round(toplam_kar, 2), round(toplam_satis, 2), toplam_tl_desi]

    for c, h in enumerate(headers):
        ttk.Label(genel_toplam_frame, text=h, font=("Arial", 9, "bold")).grid(row=0, column=c, padx=5)
    for c, v in enumerate(values):
        ttk.Label(genel_toplam_frame, text=v, font=("Arial", 9)).grid(row=1, column=c, padx=5)

genel_toplam_guncelle()

# Oran girişlerine her değişiklikte toplamları da güncelle
gyg_var.trace_add("write", lambda *args: genel_toplam_guncelle())
finans_var.trace_add("write", lambda *args: genel_toplam_guncelle())
hasar_var.trace_add("write", lambda *args: genel_toplam_guncelle())
kar_var.trace_add("write", lambda *args: genel_toplam_guncelle())

# Kaydet butonu (oranları kaydedip pencereyi kapatır)
ttk.Button(root3, text="Kaydet ve Kapat", command=oranlari_kaydet_ve_kapat).grid(row=7, column=0, columnspan=4, pady=10)

# Excel olarak kaydetme butonu
ttk.Button(root3, text="Excel Olarak Kaydet ve Kapat", command=excel_kaydet_ve_cikis).grid(row=8, column=0, columnspan=4, pady=10)

root3.mainloop()

# Oranlar çıktı
# print(f"GYG Oranı: {gyg_orani}%")
# print(f"Finansman Oranı: {finans_orani}%")
# print(f"Hasar Oranı: {hasar_orani}%")
# print(f"Kar Oranı: {kar_orani}%")
